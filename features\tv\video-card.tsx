import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import Link from 'next/link';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import { formatViews } from '@/lib/utils';

// video prors
type TVideoProps = {
  videoUrl: string;
  thumbnail: string;
  title: string;
  slug: string;
  description: string;
  date: string;
  views: number;
  category: string;
  priority?: boolean;
};

export default function VideoCard({
  thumbnail,
  title,
  slug,
  date,
  views,
  category,
  priority = false,
}: TVideoProps) {
  return (
    <Link
      className="relative flex cursor-pointer flex-col gap-4 rounded-sm bg-background p-2"
      href={`/tv/${slug}`}
      title={title}
    >
      <GlowingEffect
        disabled={false}
        glow={true}
        inactiveZone={0.01}
        proximity={64}
        spread={40}
      />
      <div className="relative overflow-hidden rounded-md ring ring-ring/10">
        <AspectRatio ratio={16 / 9}>
          <Image
            alt={title}
            className="w-full rounded-sm object-cover transition-all duration-300 hover:scale-105"
            height={500}
            priority={priority}
            src={thumbnail}
            width={500}
          />
        </AspectRatio>
      </div>
      <div className="flex flex-col gap-1">
        <h2 className="text-pretty font-medium text-lg leading-6 tracking-tight">
          {title}
        </h2>
        <p className="text-muted-foreground text-sm">
          {category || 'Uncategorized'}
        </p>
      </div>
      <div className="mt-auto flex flex-wrap items-center gap-x-3 gap-y-2 font-medium text-sm">
        <p className="text-wrap">
          {formatDistanceToNow(new Date(date), { addSuffix: true })}
        </p>
        <p className="text-muted-foreground text-sm">{formatViews(views)}</p>
      </div>
    </Link>
  );
}
