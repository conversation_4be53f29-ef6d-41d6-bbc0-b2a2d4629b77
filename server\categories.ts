import type { TNewsCategory } from "@/types";
import { getArticles } from "./articles";

export async function getCategories(): Promise<TNewsCategory[]> {
  const articles = await getArticles();

  const categories: Record<string, TNewsCategory> = {};

  for (const article of articles) {
    const categoryName = article.group;
    if (categoryName && !categories[categoryName]) {
      categories[categoryName] = {
        id: categoryName.toLowerCase(),
        title: categoryName,
      };
    }
  }

  return [
    { id: "all", title: "All" }, // Add "All" category at the start
    ...Object.values(categories),
  ];
}
