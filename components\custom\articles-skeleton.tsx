/** biome-ignore-all lint/suspicious/noArrayIndexKey: <explanation> */
import { Skeleton } from '@/components/ui/skeleton';
import { AspectRatio } from '../ui/aspect-ratio';
import { PageContainer } from './page-container';

export function ArticlesSkeleton() {
  return (
    <PageContainer className="relative w-full py-2 md:col-span-9">
      <div className="grid grid-cols-1 gap-x-3 gap-y-8 md:grid-cols-2 md:gap-y-16 lg:grid-cols-3">
        {Array.from({ length: 12 }).map((_, i) => (
          <ArticleCardSkeleton key={i} />
        ))}
      </div>
    </PageContainer>
  );
}

function ArticleCardSkeleton() {
  return (
    <div className="flex h-full flex-col gap-4 rounded-sm bg-background p-2">
      <div className="relative overflow-hidden rounded-md ring ring-ring/25">
        <AspectRatio className="w-full" ratio={16 / 9}>
          <Skeleton className="h-[282px] w-[148px] rounded-sm" />
        </AspectRatio>
      </div>
      <div className="flex flex-col gap-1">
        <Skeleton className="h-4 w-1/2" />
        <Skeleton className="h-4 w-3/4" />
      </div>
    </div>
  );
}
