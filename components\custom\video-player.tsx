/** biome-ignore-all lint/a11y/noStaticElementInteractions: <explanation> */
/** biome-ignore-all lint/nursery/noNoninteractiveElementInteractions: <explanation> */
/** biome-ignore-all lint/a11y/useKeyWithClickEvents: <explanation> */
'use client';

import { Pause, Play } from 'lucide-react';
import {
  type SyntheticEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import type { VideoPlayerProps } from '@/types';

export default function CustomVideoPlayer({
  src,
  poster,
  className,
  buttonClassName,
  aspectRatio = '16/9',
  autoPlay = false,
  loop = true,
  muted = true,
  controls = false,
  showCenterPlayButton = true,
  showHoverControls = true,
  autoPlayOnHover = false,
  pauseOnHoverLeave = false,
  buttonPosition = 'top-right',
  onPlay,
  onPause,
  onEnded,
  onHoverPlay,
  onHoverPause,
  width,
  height,
}: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isHovered, setIsHovered] = useState(false);
  const [wasPlayingBeforeHover, setWasPlayingBeforeHover] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);

  const togglePlayPause = useCallback(() => {
    if (!videoRef.current) {
      return;
    }

    if (isPlaying) {
      videoRef.current.pause();
      onPause?.();
      setWasPlayingBeforeHover(false);
    } else {
      videoRef.current.play().then(() => {
        onPlay?.();
        setWasPlayingBeforeHover(true);
      });
    }
    setIsPlaying(!isPlaying);
  }, [isPlaying, onPlay, onPause]);

  const handleVideoClick = () => {
    if (!controls) {
      togglePlayPause();
    }
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
    onEnded?.();
  };

  useEffect(() => {
    if (autoPlay && videoRef.current) {
      videoRef.current
        .play()
        .then(() => setIsPlaying(true))
        .catch(() => {
          // console.warn('Autoplay blocked');
          setIsPlaying(false);
        });
    }
  }, [autoPlay]);

  const handleMouseEnter = () => {
    setIsHovered(true);
    if (autoPlayOnHover && !isPlaying && videoRef.current) {
      setWasPlayingBeforeHover(false);
      videoRef.current.play();
      setIsPlaying(true);
      onHoverPlay?.();
    }
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    if (
      autoPlayOnHover &&
      pauseOnHoverLeave &&
      isPlaying &&
      !wasPlayingBeforeHover &&
      videoRef.current
    ) {
      videoRef.current.pause();
      setIsPlaying(false);
      onHoverPause?.();
    }
  };

  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case '16/9':
        return 'aspect-video';
      case '9/16':
        return 'aspect-[9/16]';
      case '4/3':
        return 'aspect-[4/3]';
      case '1/1':
        return 'aspect-square';
      default:
        return '';
    }
  };

  const getButtonPositionClass = () => {
    switch (buttonPosition) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 right-4';
    }
  };

  const getMimeType = (file: string) => {
    const ext = file.split('.').pop()?.split('?')[0];
    return ext ? `video/${ext}` : 'video/mp4';
  };

  const renderVideoSources = () => {
    if (Array.isArray(src)) {
      return src.map((source) => (
        <source key={source} src={source} type={getMimeType(source)} />
      ));
    }
    return <source src={src} type={getMimeType(src)} />;
  };

  const containerStyle = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
  };

  const commonButtonClass =
    'rounded-full bg-background/20 backdrop-blur-sm border-0 hover:bg-background/30 transition-all duration-200';

  return (
    <div
      className={cn(
        'group relative cursor-pointer overflow-hidden rounded-2xl',
        getAspectRatioClass(),
        className
      )}
      onClick={handleVideoClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={containerStyle}
    >
      {/* Video Element */}
      <video
        autoPlay={autoPlay}
        className="h-full w-full object-cover"
        controls={controls}
        loop={loop}
        muted={muted}
        onEnded={handleVideoEnded}
        onPause={() => setIsPlaying(false)}
        onPlay={() => setIsPlaying(true)}
        playsInline
        poster={poster}
        ref={videoRef}
      >
        {renderVideoSources()}
        Your browser does not support the video tag.
      </video>

      {/* Hover Controls */}
      {showHoverControls && (
        <div
          className={cn(
            'absolute transition-all duration-300 ease-in-out',
            getButtonPositionClass(),
            isHovered ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
          )}
        >
          <Button
            aria-label={isPlaying ? 'Pause video' : 'Play video'}
            className={cn(commonButtonClass, 'h-12 w-12', buttonClassName)}
            onClick={(e: SyntheticEvent) => {
              e.stopPropagation();
              togglePlayPause();
            }}
            size="icon"
            variant="secondary"
          >
            {isPlaying ? (
              <Pause className="h-5 w-5 fill-primary text-primary" />
            ) : (
              <Play className="ml-0.5 h-5 w-5 fill-primary text-primary" />
            )}
          </Button>
        </div>
      )}

      {/* Center Play Button */}
      {showCenterPlayButton && !isPlaying && !isHovered && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Button
            aria-label="Play video"
            className={cn(commonButtonClass, 'h-16 w-16', buttonClassName)}
            onClick={(e: SyntheticEvent) => {
              e.stopPropagation();
              togglePlayPause();
            }}
            size="icon"
            variant="secondary"
          >
            <Play className="ml-1 h-8 w-8 fill-primary text-primary" />
          </Button>
        </div>
      )}

      {/* Gradient Overlay */}
      <div
        className={cn(
          'absolute inset-0 bg-gradient-to-t from-background/20 via-transparent to-background/20 transition-opacity duration-300',
          isHovered ? 'opacity-100' : 'opacity-0'
        )}
      />
    </div>
  );
}
