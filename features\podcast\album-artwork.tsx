import { DownloadIcon, PlayIcon, ShareIcon, ThumbsUpIcon } from 'lucide-react';
import Image from 'next/image';
import { FaYoutube } from 'react-icons/fa6';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from '@/components/ui/context-menu';

import { cn } from '@/lib/utils';
import type { Album } from '@/types';

interface AlbumArtworkProps extends React.HTMLAttributes<HTMLDivElement> {
  album: Album;
  aspectRatio?: 'portrait' | 'square';
  width?: number;
  height?: number;
}

export function AlbumArtwork({
  album,
  aspectRatio = 'portrait',
  width,
  height,
  className,
  ...props
}: AlbumArtworkProps) {
  return (
    <div className={cn('flex flex-col gap-3', className)} {...props}>
      <ContextMenu>
        <ContextMenuTrigger>
          <div className="overflow-hidden rounded-md">
            <Image
              alt={album.name}
              className={cn(
                'h-auto w-auto object-cover transition-all hover:scale-105',
                aspectRatio === 'portrait' ? 'aspect-[3/4]' : 'aspect-square'
              )}
              height={height}
              src={album.cover}
              width={width}
            />
          </div>
        </ContextMenuTrigger>
        <ContextMenuContent className="w-40">
          <ContextMenuItem className="cursor-pointer">
            <PlayIcon />
            Play
          </ContextMenuItem>
          <ContextMenuItem className="cursor-pointer">
            <ThumbsUpIcon />
            Like
          </ContextMenuItem>

          <ContextMenuItem className="cursor-pointer">
            <FaYoutube />
            Open on youtube
          </ContextMenuItem>
          <ContextMenuSeparator />
          <ContextMenuItem className="cursor-pointer">
            <DownloadIcon />
            Download
          </ContextMenuItem>
          <ContextMenuItem className="cursor-pointer">
            <ShareIcon />
            Share
          </ContextMenuItem>
        </ContextMenuContent>
      </ContextMenu>
      <div className="flex flex-col gap-1 text-sm">
        <h3 className="font-medium leading-none">{album.name}</h3>
        <p className="text-muted-foreground text-xs">{album.artist}</p>
      </div>
    </div>
  );
}
