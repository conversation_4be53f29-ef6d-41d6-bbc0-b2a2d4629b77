import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import Link from 'next/link';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { GlowingEffect } from '@/components/ui/glowing-effect';
import type { TArticle } from '@/types';

export default function ArticleCard({ article }: { article: TArticle }) {
  const { title, slug, publishedAt, coverImage, group } = article;
  return (
    <Link
      className="group relative flex h-full cursor-pointer flex-col gap-4 rounded-sm bg-background md:p-2"
      href={`/article/${slug}`}
    >
      <GlowingEffect
        className="hidden md:block"
        disabled={false}
        glow={true}
        inactiveZone={0.01}
        proximity={64}
        spread={40}
      />

      <div className="relative w-full overflow-hidden rounded-md ring ring-ring/25">
        <AspectRatio className="w-full" ratio={16 / 9}>
          <Image
            alt={`Featured image for article: ${title}`}
            className="rounded-sm object-cover transition-transform duration-300 group-hover:scale-105"
            fill
            priority
            src={coverImage}
          />
        </AspectRatio>
      </div>

      <h2 className="line-clamp-2 text-pretty pr-5 font-medium text-lg leading-6 tracking-tight ">
        {title}
      </h2>
      <div className="mt-auto md:pr-12 ">
        <div className="mt-auto flex flex-wrap items-center gap-x-3 gap-y-2 font-medium text-sm">
          <p className="text-wrap transition-all duration-500 group-hover:tracking-wider">
            {group}
          </p>
          <p className="text-muted-foreground text-sm">
            {formatDistanceToNow(new Date(publishedAt), {
              addSuffix: true,
            })}
          </p>
        </div>
      </div>
    </Link>
  );
}
