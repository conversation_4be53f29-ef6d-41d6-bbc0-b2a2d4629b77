import { Suspense } from 'react';
import { ArticlesSkeleton } from '@/components/custom/articles-skeleton';
import { PageContainer } from '@/components/custom/page-container';
import { AdsList } from '@/features/home/<USER>';
import ArticleLists from '@/features/home/<USER>';
import ArticlesCategories from '@/features/home/<USER>';
import { TopImagesAds } from '@/features/home/<USER>';
import { TopTextAds } from '@/features/home/<USER>';

export default async function Home({
  searchParams,
}: {
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const { category } = (await searchParams) as { category: string };
  return (
    <>
      <TopTextAds />
      <TopImagesAds />
      <Suspense fallback={<div>Loading...</div>}>
        <ArticlesCategories searchParams={searchParams} />
      </Suspense>
      <PageContainer className="relative py-8 pb-10 md:py-6 md:pb-20">
        <div className="relative grid w-full grid-cols-1 gap-8 md:grid-cols-12 md:gap-0">
          <Suspense fallback={<ArticlesSkeleton />}>
            <ArticleLists category={category} />
          </Suspense>
          <AdsList />
        </div>
      </PageContainer>
    </>
  );
}
