'use client';

import { <PERSON><PERSON><PERSON><PERSON>, Editor<PERSON>ontex<PERSON>, useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { cn } from '@/lib/utils';

// --- Tiptap Node ---

import '@/features/tiptap/tiptap-node/code-block-node/code-block-node.scss';
import '@/features/tiptap/tiptap-node/image-node/image-node.scss';
import '@/features/tiptap/tiptap-node/list-node/list-node.scss';
import '@/features/tiptap/tiptap-node/paragraph-node/paragraph-node.scss';

//
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import js from 'highlight.js/lib/languages/javascript';
import ts from 'highlight.js/lib/languages/typescript';
import { all, createLowlight } from 'lowlight';

const lowlight = createLowlight(all);
lowlight.register({ js, ts });

interface SimpleEditorProps {
  content: string;
  words: number;
  onChange?: (content: string, words: number) => void;
  editable?: boolean;
  className?: string;
  id: string;
  disabled?: boolean;
}

export default function SimpleEditor({
  content,
  editable = false,
  className,
  disabled,
}: SimpleEditorProps) {
  const editor = useEditor({
    editable: editable && !disabled,
    extensions: [
      StarterKit,
      CodeBlockLowlight.configure({
        lowlight,
      }),
    ],
    immediatelyRender: false,
    editorProps: {
      attributes: {
        autocomplete: 'off',
        autocorrect: 'off',
        autocapitalize: 'off',
        'aria-label': 'Main content area, start typing to enter text.',
      },
    },
    content,
  });

  return (
    <EditorContext.Provider value={{ editor }}>
      <div className="relative">
        <div className={cn('w-full', className)}>
          <EditorContent
            className={cn('whitespace-pre-wrap break-words')}
            disabled={disabled}
            editor={editor}
            role="presentation"
          />
        </div>
      </div>
    </EditorContext.Provider>
  );
}
