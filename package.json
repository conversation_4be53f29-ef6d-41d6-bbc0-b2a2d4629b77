{"name": "info", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "ultracite format"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-code-block-lowlight": "^3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/react": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "geist": "^1.4.2", "highlight.js": "^11.11.1", "lowlight": "^3.3.0", "lucide-react": "^0.525.0", "motion": "^12.23.6", "next": "15.4.1", "next-themes": "^0.4.6", "next-video": "^2.2.1", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-icons": "^5.5.0", "react-use-measure": "^2.1.7", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^4.0.5"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@tailwindcss/postcss": "^4", "@types/canvas-confetti": "^1.9.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "sass": "^1.89.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5", "ultracite": "5.0.36"}}