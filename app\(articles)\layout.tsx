import { SiteFooter } from '@/components/layout/site-footer';
import { SiteHeader } from '@/components/layout/site-header';

export default function BlogLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="relative z-10 flex min-h-svh flex-col bg-background">
      <SiteHeader isBlog={true} />
      <main className="relative flex min-h-svh flex-1 flex-col">
        {children}
      </main>
      <SiteFooter />
    </div>
  );
}
