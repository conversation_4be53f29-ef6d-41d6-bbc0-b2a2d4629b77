import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel';
import { allCategories } from '@/config/app';
import { VideoBtn } from './video-category';

export default function SvideoCategory() {
  return (
    <div className="relativ -ml-3 sticky top-14 z-10 bg-background">
      <Carousel
        opts={{
          align: 'start',
          loop: true,
          slidesToScroll: 3,
        }}
      >
        <div className="p- relative flex w-full items-center overflow-hidden">
          <CarouselContent className="-ml-1">
            {allCategories.map((category, index) => (
              <CarouselItem
                className="w-fit basis-auto pl-2"
                key={category.title}
              >
                <VideoBtn
                  className="rounded-sm"
                  index={index}
                  link={category.title.toLowerCase()}
                  name={category.title}
                />
              </CarouselItem>
            ))}
          </CarouselContent>
        </div>
      </Carousel>
    </div>
  );
}
