import { formatDistanceToNow } from 'date-fns';
import Image from 'next/image';
import Link from 'next/link';
export default function SVideoCard({
  thumbnail,
  title,
  slug,
  description,
  date,
}: {
  thumbnail: string;
  title: string;
  slug: string;
  description: string;
  date: string;
}) {
  return (
    <Link className="relative w-full " href={`/tv/${slug}`} title={title}>
      <div className="flex justify-between gap-4">
        <div className="relative aspect-video min-h-[100px] overflow-hidden rounded-sm object-cover">
          <Image alt={title} className="object-cover" fill src={thumbnail} />
        </div>

        <div className="flex flex-1 flex-col gap-1 py-2 pr-2">
          <h2 className="font-semibold text-base leading-5 tracking-tighter">
            {title}
          </h2>
          <p className="line-clamp-1 text-muted-foreground text-sm leading-4 tracking-tight ">
            {description}
          </p>
          <div className="mt-auto flex flex-col gap-1">
            <p className="text-wrap text-muted-foreground text-xs">
              {formatDistanceToNow(new Date(date), { addSuffix: true })}
            </p>
          </div>
        </div>
      </div>
    </Link>
  );
}
