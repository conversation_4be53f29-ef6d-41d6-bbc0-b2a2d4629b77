import {
  ChartNoAxesColumnDecreasingIcon,
  ClapperboardIcon,
  CoinsIcon,
  FileTextIcon,
  GlobeIcon,
  HouseIcon,
  ImageIcon,
  ListMusicIcon,
  Mailbox,
  MegaphoneIcon,
  MountainIcon,
  NotebookPenIcon,
  UsersIcon,
} from "lucide-react";
import { FaEarthAfrica, FaEarthAmericas, FaEarthEurope } from "react-icons/fa6";
import poster from "@/public/thumbnail.jpg";
import type {
  Ad,
  Article,
  TCategory,
  TFooterLink,
  TImageCard,
  TNavItem,
  TNewsLink,
  TVideo,
} from "@/types";

export const topTextMessages = [
  {
    text: "Hari amasiganwa ya magare ari kubera <PERSON>nko ku mwaka wa 2025",
    link: "https://hextaui.com",
    tooltipText: "🚴‍♀️ Info Tv 🚲",
  },
  {
    text: "Irushanwa ry'ikoranabuhanga rigiye kubera i Kigali mu kwezi kwa Nyakanga",
    link: "https://techchallenge.rw",
    tooltipText: "💻 Tech Challenge 🌐",
  },
  {
    text: "Igitaramo cy'abahanzi kizabera BK Arena tariki 20 Kamena",
    link: "https://bkarenalive.rw",
    tooltipText: "🎤 BK Arena 🎶",
  },
];
export const navItems: TNavItem[] = [
  {
    href: "/",
    label: "Home",
    icon: HouseIcon,
  },
  {
    href: "/about",
    label: "About",
    icon: NotebookPenIcon,
  },
  {
    href: "/staff",
    label: "Staffs",
    icon: UsersIcon,
  },
  {
    href: "/gallery",
    label: "Gallery",
    icon: ImageIcon,
  },
  {
    href: "/tv",
    label: "TV",
    icon: ClapperboardIcon,
  },
  {
    href: "/podcast",
    label: "Podcast",
    icon: ListMusicIcon,
  },
  {
    href: "/ads",
    label: "Ads",
    icon: MegaphoneIcon,
  },
  {
    href: "/#stock",
    label: "Stock",
    icon: CoinsIcon,
  },
];
export const newsLinks: TNewsLink[] = [
  {
    title: "News",
    links: [
      {
        title: "Breaking News",
        href: "/",
        icon: Mailbox,
        external: false,
      },
      {
        title: "Latest News",
        href: "/",
        icon: FileTextIcon,
        external: false,
      },
      {
        title: "Top Stories",
        href: "/",
        icon: ChartNoAxesColumnDecreasingIcon,
        external: false,
      },
      {
        title: "Local News",
        href: "/",
        icon: GlobeIcon,
        external: false,
      },
    ],
  },
  {
    title: "Region",
    links: [
      {
        title: "Rwanda",
        href: "/",
        external: false,
        icon: MountainIcon,
      },
      {
        title: "Africa",
        href: "/",
        external: false,
        icon: FaEarthAfrica,
      },
      {
        title: "Usa",
        href: "/",
        external: false,
        icon: FaEarthAmericas,
      },
      {
        title: "Europe",
        href: "/",
        external: false,
        icon: FaEarthEurope,
      },
    ],
  },
];
export const footerLinks: TFooterLink[] = [
  {
    title: "Newsroom",
    links: [
      { name: "Latest News", href: "/", external: false },
      { name: "Top Stories", href: "/", external: false },
      { name: "Editor's Picks", href: "/", external: false },
    ],
  },
  {
    title: "Company",
    links: [
      { name: "About Us", href: "/", external: false },
      { name: "Careers", href: "/", external: false },
      { name: "Press", href: "/", external: false },
      { name: "Contact", href: "/", external: false },
    ],
  },
  {
    title: "For Business",
    links: [
      { name: "Advertise with Us", href: "/", external: false },
      { name: "Media Kit", href: "/", external: false },
      { name: "Partner with Us", href: "/", external: false },
    ],
  },
  {
    title: "More",
    links: [
      { name: "Newsletter", href: "/", external: false },
      { name: "Mobile App", href: "/", external: false },
      { name: "RSS Feeds", href: "/", external: false },
      { name: "Help Center", href: "/", external: false },
    ],
  },
  {
    title: "Terms & Policies",
    links: [
      { name: "Terms of Use", href: "/", external: false },
      { name: "Privacy Policy", href: "/", external: false },
      { name: "Cookie Policy", href: "/", external: false },
      { name: "Editorial Policy", href: "/", external: false },
    ],
  },
  {
    title: "Safety",
    links: [
      { name: "Fact-Checking", href: "/", external: false },
      { name: "Corrections", href: "/", external: false },
      { name: "Trust & Transparency", href: "/", external: false },
    ],
  },
  {
    title: "Follow Us",
    links: [
      { name: "Facebook", href: "/", external: true },
      { name: "Twitter", href: "/", external: true },
      { name: "Instagram", href: "/", external: true },
      { name: "YouTube", href: "/", external: true },
    ],
  },
  {
    title: "Sections",
    links: [
      { name: "Politics", href: "/", external: false },
      { name: "Business", href: "/", external: false },
      { name: "Technology", href: "/", external: false },
      { name: "Health", href: "/", external: false },
    ],
  },
  {
    title: "Resources",
    links: [
      { name: "Media Resources", href: "/", external: false },
      { name: "Author Guidelines", href: "/", external: false },
      { name: "News Archive", href: "/", external: false },
    ],
  },
  {
    title: "Community",
    links: [
      { name: "Events", href: "/", external: false },
      { name: "Reader Stories", href: "/", external: false },
      { name: "Submit News", href: "/", external: false },
    ],
  },
];

export const galleryImages: TImageCard[] = [
  {
    title: "Mountain Escape",
    images: [
      {
        imageUrl: "/video/election.jpg",
        title: "Mountain Escapet",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/video/citydev.jpg",
        title: "Mountain Escapez",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/video/talk.jpg",
        title: "In the mountains",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
    ],
  },
  {
    title: "Forest Retreat",
    images: [
      {
        imageUrl: "/articles/ai.jpg",
        title: "Forest Retreat",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/articles/tech2.jpg",
        title: "In the mountains",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/articles/greentech.jpg",
        title: "In the mountainz",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/thumbnail.jpg",
        title: "In the mountaint",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
    ],
  },
  {
    title: "In the mountains",
    images: [
      {
        imageUrl: "/video/citydev.jpg",
        title: "In the mountains gollira",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/video/election.jpg",
        title: "In the mountains kigali",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/video/talk.jpg",
        title: "In the mountains rwanda",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/articles/ai.jpg",
        title: "In the mountains gasogi",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/articles/tech2.jpg",
        title: "In the mountains today",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
      {
        imageUrl: "/articles/greentech.jpg",
        title: "In the mountains park",
        description: "$1299 / night",
        date: "2025-06-05T10:00:00Z",
      },
    ],
  },
];

export const tvVideos: TVideo[] = [
  {
    title: "Breaking News: Market Crash",
    slug: "breaking-news-market-crash",
    description:
      "An in-depth look at the recent market downturn and its impact.",
    category: "News",
    thumbnail: poster.src,
    videoUrl: "/video2.mp4",
    date: "2025-06-05T10:00:00Z",
    views: 1203,
  },
  {
    title: "Elections 2025: What to Expect",
    slug: "elections-2025-what-to-expect",
    description: "A preview of the upcoming elections and major candidates.",
    category: "News",
    thumbnail: "/video/election.jpg",
    videoUrl:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iaeec9V8ALRBu7tpC64bnzmT8iQrcyOvla19hH",
    date: "2025-06-04T14:30:00Z",
    views: 875,
  },
  {
    title: "City Developments Update",
    slug: "city-developments-update",
    description: "Latest news on infrastructure and urban development.",
    category: "News",
    thumbnail: "/video/citydev.jpg",
    videoUrl:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iabX6jwh1pPt62vRZzly7h0B9mCQxGfoijIeWK",
    date: "2025-06-03T09:15:00Z",
    views: 642,
  },

  {
    title: "Tech Talk: AI in 2025",
    slug: "tech-talk-ai-in-2025",
    description: "Exploring how AI is shaping industries and daily life.",
    category: "Podcast",
    thumbnail: "/video/talk.jpg",
    videoUrl:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iaNHZHhNSsuj1StCv5PwH0m4Kchx8YQEXAMUoF",
    date: "2025-06-02T11:00:00Z",
    views: 1094,
  },
];

export const allCategories: TCategory[] = [
  { title: "All" },
  { title: "Politics" },
  { title: "Business" },
  { title: "Technology" },
  { title: "Sport" },
  { title: "Entertainment" },
  { title: "Health" },
  { title: "Science" },
  { title: "World" },
  { title: "Education" },
  { title: "Environment" },
  { title: "Lifestyle" },
  { title: "Travel" },
  { title: "Crime" },
  { title: "Opinion" },
  { title: "Culture" },
];

export const articles: Article[] = [
  {
    title: "Global Markets React to Sudden Policy Shift",
    slug: "global-markets-react-to-sudden-policy-shift",
    description:
      "A cloud-based software engineering agent that can work on many tasks in parallel, powered by codex-1. Available to ChatGPT Pro, Team, and Enterprise users today, and Plus users soon.",
    cover_media: "/thumbnail.jpg",
    cover_type: "Image",
    author: {
      name: "John Doe",
    },

    link: "/news/thinking-components",
    date: "2025-06-07T10:00:00Z",
    category: "Politics",
    views: 1000,
  },
  {
    title: "Championship Showdown: Rivals Battle for Glory",
    slug: "championship-showdown-rivals-battle-for-glory",
    description:
      "A cloud-based software engineering agent that can work on many tasks in parallel, powered by codex-1. Available to ChatGPT Pro, Team, and Enterprise users today, and Plus users soon.",
    cover_media: "/articles/greentech.jpg",
    cover_type: "Image",
    author: {
      name: "John Doe",
    },
    link: "/news/functional-fury",
    date: "2025-06-02T10:00:00Z",
    category: "Sports",
    views: 1000,
  },
  {
    title: "Tech Giants Unveil Next-Gen Devices at Expo",
    slug: "tech-giants-unveil-next-gen-devices-at-expo",
    description:
      "A cloud-based software engineering agent that can work on many tasks in parallel, powered by codex-1. Available to ChatGPT Pro, Team, and Enterprise users today, and Plus users soon.",
    cover_media: "/articles/tech2.jpg",
    cover_type: "Image",
    author: {
      name: "John Doe",
    },
    link: "/news/react-rendezvous",
    date: "2025-06-05T10:00:00Z",
    category: "Technology",
    views: 1000,
  },
  {
    title: "Climate Talks Enter Critical Phase Amid Pressure",
    slug: "climate-talks-enter-critical-phase-amid-pressure",
    description:
      "A cloud-based software engineering agent that can work on many tasks in parallel, powered by codex-1. Available to ChatGPT Pro, Team, and Enterprise users today, and Plus users soon.",
    cover_media: "/articles/climate.jpg",
    cover_type: "Image",
    author: {
      name: "John Doe",
    },

    link: "/news/stateful-symphony",
    date: "2025-06-03T10:00:00Z",
    category: "Environment",
    views: 1000,
  },
  {
    title: "Startup Secures Record Funding for Green Tech",
    slug: "startup-secures-record-funding-for-green-tech",
    description:
      "A cloud-based software engineering agent that can work on many tasks in parallel, powered by codex-1. Available to ChatGPT Pro, Team, and Enterprise users today, and Plus users soon.",
    cover_media: "/articles/greentech.jpg",
    cover_type: "Image",
    author: {
      name: "John Doe",
    },

    link: "/news/async-awakenings",
    date: "2025-06-01T10:00:00Z",
    category: "Business",
    views: 1000,
  },
  {
    title: "AI Breakthrough Promises Faster Medical Diagnosis",
    slug: "ai-breakthrough-promises-faster-medical-diagnosis",
    description:
      "A cloud-based software engineering agent that can work on many tasks in parallel, powered by codex-1. Available to ChatGPT Pro, Team, and Enterprise users today, and Plus users soon.",
    cover_media: "/articles/ai.jpg",
    cover_type: "Image",
    author: {
      name: "John Doe",
    },
    link: "/news/the-art-of-reusability",
    date: "2025-06-06T10:00:00Z",
    category: "Health",
    views: 1000,
  },
];

export const ads: Ad[] = [
  {
    companyName: "Buy Today",
    cover:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iaAI5pJzLrjsXEGDV842Knqk6bzweC5RxlNHTi",
    coverType: "img",
    createdAt: "2025-05-29T08:45:00Z",
    link: "",
  },
  {
    companyName: "Rathon dev",
    cover:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iaeec9V8ALRBu7tpC64bnzmT8iQrcyOvla19hH",
    coverType: "vid",
    createdAt: "2025-06-03T14:20:00Z",
    link: "https://rathon.vercel.app",
  },
  {
    companyName: "Ship faster",
    cover:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iaHdM2PCgJkUs3OlGdqPaneRDCiyFv4rjLMW2S",
    coverType: "img",
    createdAt: "2025-06-01T10:30:00Z",
    link: "",
  },
  {
    companyName: "BlackFriday",
    cover:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iabX6jwh1pPt62vRZzly7h0B9mCQxGfoijIeWK",
    coverType: "vid",
    createdAt: "2025-06-05T16:00:00Z",
    link: "",
  },

  {
    companyName: "New ads",
    cover:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iaTdv94HxmCU5rw2uxflT8PgYp9sXSVja3FLe4",
    coverType: "img",
    createdAt: "2025-06-03T14:20:00Z",
    link: "",
  },
  {
    companyName: "Claim your reward",
    cover:
      "https://3u39ha98bi.ufs.sh/f/CoX6DXpfh7iaHTfS5eCgJkUs3OlGdqPaneRDCiyFv4rjLMW2",
    coverType: "img",
    createdAt: "2025-06-03T14:20:00Z",
    link: "",
  },
];
