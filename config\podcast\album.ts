import type { Album } from '@/types';

export const listenNowAlbums: Album[] = [
  {
    name: '<PERSON><PERSON>',
    artist: '<PERSON>',
    cover: '/articles/ai.jpg',
  },
  {
    name: 'As<PERSON> Awakenins',
    artist: '<PERSON>',
    cover: '/articles/tech2.jpg',
  },
  {
    name: 'The Art of Reusabil',
    artist: '<PERSON>',
    cover: '/articles/greentech.jpg',
  },
  {
    name: 'State<PERSON>',
    artist: '<PERSON>',
    cover: '/articles/climate.jpg',
  },
  {
    name: 'Async Awakeningsj',
    artist: '<PERSON>',
    cover: '/video/election.jpg',
  },
  {
    name: 'The Art of Reusabilityh',
    artist: '<PERSON>',
    cover: '/video/citydev.jpg',
  },
  {
    name: 'Stateful <PERSON>',
    artist: '<PERSON>',
    cover: '/video/talk.jpg',
  },
];

export const madeForYouAlbums: Album[] = [
  {
    name: 'Thinking Componentst',
    artist: '<PERSON>',
    cover: '/thumbnail.jpg',
  },
  {
    name: 'Functional <PERSON><PERSON>',
    artist: '<PERSON>',
    cover: '/articles/ai.jpg',
  },
  {
    name: '<PERSON><PERSON>',
    artist: '<PERSON>',
    cover: '/articles/tech2.jpg',
  },
  {
    name: 'Stateful Symphoni',
    artist: 'Beth Binary',
    cover: '/articles/greentech.jpg',
  },
  {
    name: 'Async Awakeningz',
    artist: 'Nina Netcode',
    cover: '/articles/climate.jpg',
  },
  {
    name: 'The Art of Reusabilit',
    artist: 'Lena Logic',
    cover: '/video/election.jpg',
  },
  {
    name: 'Stateful Symphony',
    artist: 'Beth Binary',
    cover: '/video/citydev.jpg',
  },
  {
    name: 'Async Awakenings',
    artist: 'Nina Netcode',
    cover: '/video/talk.jpg',
  },
  {
    name: 'The Art of Reusability',
    artist: 'Lena Logic',
    cover: '/articles/ai.jpg',
  },
];
