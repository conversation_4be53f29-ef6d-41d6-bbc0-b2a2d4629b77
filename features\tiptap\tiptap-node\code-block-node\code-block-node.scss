.tiptap.ProseMirror {
  --tt-inline-code-bg-color: var(--tt-gray-light-a-100);
  --tt-inline-code-text-color: var(--tt-gray-light-a-700);
  --tt-inline-code-border-color: var(--tt-gray-light-a-200);
  --tt-codeblock-bg: var(--tt-gray-light-a-50);
  --tt-codeblock-text: var(--tt-gray-light-a-800);
  --tt-codeblock-border: var(--tt-gray-light-a-200);

  .dark & {
    --tt-inline-code-bg-color: var(--tt-gray-dark-a-100);
    --tt-inline-code-text-color: var(--tt-gray-dark-a-700);
    --tt-inline-code-border-color: var(--tt-gray-dark-a-200);
    --tt-codeblock-bg: var(--tt-gray-dark-a-50);
    --tt-codeblock-text: var(--tt-gray-dark-a-800);
    --tt-codeblock-border: var(--tt-gray-dark-a-200);
  }
}

/* =====================
   CODE FORMATTING
   ===================== */
.tiptap.ProseMirror {
  // Inline code
  code {
    background: var(--black);
    border-radius: 0.5rem;
    color: var(--white);
    font-family: "JetBrainsMono", monospace;
    font-size: 0.8rem;
    margin: 0 0.125rem;
    padding: 0.125rem 0.25rem;
    caret-color: var(--white);
  }

  // Code blocks
  pre {
    background: var(--black);
    border-radius: 0.5rem;
    color: var(--white);
    font-family: "JetBrainsMono", monospace;
    margin: 1.5rem 0;
    padding: 0.75rem 1rem;

    code {
      background: none;
      color: inherit;
      font-size: 0.8rem;
      padding: 0;
    }
    // /* Code styling */
    // .hljs-comment,
    // .hljs-quote {
    //   color: #616161;
    // }

    // .hljs-variable,
    // .hljs-template-variable,
    // .hljs-attribute,
    // .hljs-tag,
    // .hljs-regexp,
    // .hljs-link,
    // .hljs-name,
    // .hljs-selector-id,
    // .hljs-selector-class {
    //   color: #f98181;
    // }

    // .hljs-number,
    // .hljs-meta,
    // .hljs-built_in,
    // .hljs-builtin-name,
    // .hljs-literal,
    // .hljs-type,
    // .hljs-params {
    //   color: #fbbc88;
    // }

    // .hljs-string,
    // .hljs-symbol,
    // .hljs-bullet {
    //   color: #b9f18d;
    // }

    // .hljs-title,
    // .hljs-section {
    //   color: #faf594;
    // }

    // .hljs-keyword,
    // .hljs-selector-tag {
    //   color: #70cff8;
    // }

    // .hljs-emphasis {
    //   font-style: italic;
    // }

    // .hljs-strong {
    //   font-weight: 700;
    // }
  }
}
