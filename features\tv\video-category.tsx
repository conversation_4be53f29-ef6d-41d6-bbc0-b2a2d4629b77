import Link from 'next/link';
import { PageContainer } from '@/components/custom/page-container';
import { Button } from '@/components/ui/button';
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@/components/ui/carousel';
import { allCategories } from '@/config/app';
import { cn } from '@/lib/utils';
import NextBtn from '../home/<USER>';
export default function VideoCategory() {
  return (
    <PageContainer className="pt-8 md:pt-16">
      <div className="relative">
        <Carousel
          className="flex w-full max-w-5xl items-center lg:max-w-6xl"
          opts={{
            align: 'start',
            loop: true,
            slidesToScroll: 3,
          }}
        >
          <div className="relative flex w-full items-center overflow-hidden p-2">
            <CarouselContent className="-ml-1">
              {allCategories.map((category, index) => (
                <CarouselItem
                  className="w-fit basis-auto pl-2"
                  key={category.title}
                >
                  <VideoBtn
                    className="rounded-sm"
                    index={index}
                    link={category.title.toLowerCase()}
                    name={category.title}
                  />
                </CarouselItem>
              ))}
            </CarouselContent>
            <NextBtn className="-right-2 top-0 hidden md:block" />
          </div>
        </Carousel>
      </div>
    </PageContainer>
  );
}

export function VideoBtn({
  index,
  name,
  link,
  className,
}: {
  index: number;
  name: string;
  link: string;
  className?: string;
}) {
  const variant = index === 0 ? 'default' : 'secondary';
  return (
    <Button
      asChild
      className={cn('h-full w-fit rounded-2xl px-3 py-1.5', className)}
      variant={variant}
    >
      <Link className="flex h-full w-fit" href={link}>
        <span>{name}</span>
      </Link>
    </Button>
  );
}
