import { Armchair } from 'lucide-react';
import Link from 'next/link';
import { siteConfig } from '@/config/site';
import { getArticles } from '@/server/articles';
import { getCategories } from '@/server/categories';
import { Button } from '../ui/button';
import { ModeSwitcher } from '../ui/mode-switcher';
import { CommandMenu } from './command-menu';
import HeaderContactForm from './header-contact-form';
import { MainNav } from './main-nav';
import { MobileNav } from './mobile-nav';

interface SiteHeaderProps {
  isBlog?: boolean;
}
export async function SiteHeader({ isBlog = false }: SiteHeaderProps) {
  const articles = await getArticles();
  const categories = await getCategories();

  return (
    <header className="sticky top-0 z-50 w-full bg-background/40 backdrop-blur-lg supports-backdrop-blur:bg-background/90">
      <div className="container-wrapper sm:px-6 lg:px-0">
        <div className="container flex h-14 items-center gap-2 md:gap-4">
          <MobileNav categories={categories} isBlog={isBlog} />
          <Button
            asChild
            className="hidden size-8 lg:flex"
            size="icon"
            variant="ghost"
          >
            <Link href="/">
              <Armchair className="size-7 font-bold" />
              <span className="sr-only">{siteConfig.name}</span>
            </Link>
          </Button>
          <MainNav
            categories={categories}
            className="hidden lg:flex"
            isBlog={isBlog}
          />
          <div className="ml-auto flex items-center gap-2 md:flex-1 md:justify-end">
            <div className="hidden w-full flex-1 md:flex md:w-auto md:flex-none">
              <CommandMenu
                articles={articles}
                categories={categories}
                isBlog={isBlog}
              />
            </div>
            <nav className="flex items-center gap-4">
              <HeaderContactForm />
              <ModeSwitcher />
            </nav>
          </div>
        </div>
      </div>
    </header>
  );
}
