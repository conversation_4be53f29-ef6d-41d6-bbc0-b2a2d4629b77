import { PageContainer } from '@/components/custom/page-container';
import { BlurFade } from '@/components/ui/blur-fade';
import { galleryImages } from '@/config/app';
import { ImageCard } from './image-card';

export function GalleryList() {
  return (
    <PageContainer className="py-8 md:py-16 lg:py-20">
      <div className="flex flex-col gap-6 md:gap-10 lg:gap-20">
        {galleryImages.map((image) => (
          <div className="flex flex-col gap-4 md:gap-10" key={image.title}>
            <h2 className="text-left font-bold text-2xl">{image.title}</h2>
            <div className="grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3 lg:gap-y-10">
              {image.images.map((img, index) => (
                <BlurFade delay={0.25 + index * 0.05} inView key={img.title}>
                  <ImageCard {...img} />
                </BlurFade>
              ))}
            </div>
          </div>
        ))}
      </div>
    </PageContainer>
  );
}
