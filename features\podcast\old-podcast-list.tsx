import { ChevronRightIcon } from 'lucide-react';
import { PageContainer } from '@/components/custom/page-container';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { madeForYouAlbums } from '@/config/podcast/album';
import { AlbumArtwork } from './album-artwork';

export default function OldPodcastList() {
  return (
    <PageContainer className="py-8 md:py-16">
      <div className="flex flex-col gap-6 lg:gap-10">
        <div className="flex items-center justify-between">
          <div className="flex flex-col gap-1">
            <h2 className="font-semibold text-2xl tracking-tight">
              Made for You
            </h2>
            <p className="text-muted-foreground text-sm">
              Your personal playlists. Updated daily.
            </p>
          </div>
          <div className="flex items-center gap-1 text-foreground transition-all duration-300 hover:text-muted-foreground">
            <span className="text-sm">See all</span>
            <ChevronRightIcon className="size-4" />
          </div>
        </div>
        <div className="relative">
          <ScrollArea>
            <div className="flex gap-4 pb-4">
              {madeForYouAlbums.map((album) => (
                <AlbumArtwork
                  album={album}
                  aspectRatio="square"
                  className="w-[150px]"
                  height={150}
                  key={album.name}
                  width={150}
                />
              ))}
            </div>
            <ScrollBar className="h-1.5" orientation="horizontal" />
          </ScrollArea>
        </div>
      </div>
    </PageContainer>
  );
}
