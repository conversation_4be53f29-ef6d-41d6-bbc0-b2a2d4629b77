export type TFooterLink = {
  title: string;
  links: { name: string; href: string; external: boolean }[];
};
export type TNavItem = {
  href: string;
  label: string;
  external?: boolean;
  icon: ForwardRefExoticComponent<
    Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
  >;
};
export type TImageCard = {
  title: string;
  images: {
    imageUrl: string;
    title: string;
    description: string;
    date: string; // ISO string format
  }[];
};

export type TVideo = {
  title: string;
  slug: string;
  description: string;
  category: string;
  thumbnail: string;
  videoUrl: string;
  date: string; // ISO string format
  views: number;
};

export interface VideoPlayerProps {
  src: string | string[];
  poster?: string;
  className?: string;
  buttonClassName?: string;
  aspectRatio?: "16/9" | "9/16" | "4/3" | "1/1" | "auto";
  autoPlay?: boolean;
  loop?: boolean;
  muted?: boolean;
  controls?: boolean;
  showCenterPlayButton?: boolean;
  showHoverControls?: boolean;
  autoPlayOnHover?: boolean;
  pauseOnHoverLeave?: boolean;
  buttonPosition?: "top-left" | "top-right" | "bottom-left" | "bottom-right";
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
  onHoverPlay?: () => void;
  onHoverPause?: () => void;
  width?: string | number;
  height?: string | number;
}
export type Article = {
  title: string;
  slug: string;
  description: string;
  cover_media: string;
  cover_type: "Image" | "Video";
  author: {
    name: string;
    image?: string;
  };
  link: string;
  date: string;
  category: string;
  views: number;
};

export type TNewsLink = {
  title: string;
  links: {
    title: string;
    href: string;
    icon: ForwardRefExoticComponent<
      Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
    >;
    external: boolean;
  }[];
};

export type Ad = {
  companyName: string;
  cover: string;
  coverType: "img" | "vid";
  createdAt: string;
  link?: string;
  isActive?: boolean;
};

export interface Album {
  name: string;
  artist: string;
  cover: string;
}

// export type TArticle = {
//   id: string;
//   title: string;
//   slug: string;
//   description: string | null;
//   content: string;
//   cover_media: string | null;
//   words: number;
//   publishedAt: Date | null;
//   createdAt: Date;
//   updatedAt: Date;
//   user: {
//     id: string;
//     name: string;
//     image: string | null;
//   };
//   category: {
//     id: string;
//     title: string;
//   } | null;
// };
export type TNewsCategory = {
  id: string;
  title: string;
};

export type TCategory = {
  title: string;
};

export type TArticle = {
  title: string;
  slug: string;
  description: string | undefined;
  content: string | undefined;
  words: number | undefined;
  coverImage: string;
  status: "draft" | "staged" | "approved" | "published" | "deleted";
  group: string;
  authorId: string;
  publishedAt: number;
};

export type TMember = {
  id: string;
  avatarUrl: string | null | undefined;
  _creationTime: number;
  name?: string | undefined;
  image?: string | undefined;
  email?: string | undefined;
  phone?: string | undefined;
  username?: string | undefined;
  bio?: string | undefined;
  role?: "author" | "admin" | "media-manager" | "ads-manager" | undefined;
  coverImage?: string | undefined;
  socialLinks?:
    | {
        name: string;
        url: string;
      }[]
    | undefined;
};
