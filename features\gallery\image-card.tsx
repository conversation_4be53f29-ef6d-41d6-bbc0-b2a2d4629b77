'use client';
import { formatDistanceToNow } from 'date-fns';
import { Download, Share2, X } from 'lucide-react';
import Image from 'next/image';
import { useState } from 'react';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { DirectionAwareHover } from '@/components/ui/direction-aware-hover';
import { Lens } from '@/components/ui/lens';

type TImageCard = {
  imageUrl: string;
  title: string;
  description: string;
  date: string;
};

export function ImageCard({ imageUrl, title, description, date }: TImageCard) {
  const [showLens, setShowLens] = useState(true);

  const handleShare = async () => {
    if (navigator.share) {
      await navigator.share({ title, text: description, url: imageUrl });
    } else {
      await navigator.clipboard.writeText(imageUrl);
      alert('Image URL copied to clipboard.');
    }
  };

  const ImageWithOrWithoutLens = showLens ? (
    <Lens className="cursor-grab rounded-md">
      <ImageContainer imageUrl={imageUrl} title={title} />
    </Lens>
  ) : (
    <ImageContainer imageUrl={imageUrl} title={title} />
  );

  return (
    <Dialog>
      <DialogTrigger>
        <div className="relative flex cursor-pointer items-center justify-start rounded-lg text-left">
          <DirectionAwareHover imageUrl={imageUrl}>
            <p className="font-bold text-xl">{title}</p>
            <p className="font-normal text-sm">{description}</p>
          </DirectionAwareHover>
        </div>
      </DialogTrigger>

      <DialogContent className="min-h-[40svh] w-full max-w-5xl rounded-none p-4 md:mx-0 md:h-auto md:min-h-[60svh] md:rounded-lg">
        <DialogHeader className="sr-only">
          <DialogTitle className="font-medium text-lg leading-6 tracking-tight">
            {title}
          </DialogTitle>
          <DialogDescription className="text-muted-foreground text-sm">
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="relative flex flex-col gap-2">
          {/* Show lens toggle */}

          {/* Conditional Lens display */}
          {ImageWithOrWithoutLens}

          {/* Actions */}
          <div className="mt-2 flex justify-end gap-2">
            <div className="flex justify-end">
              <Button
                aria-label="Toggle lens"
                onClick={() => setShowLens((prev) => !prev)}
                size="sm"
                variant="ghost"
              >
                {showLens ? 'Disable Lens' : 'Enable Lens'}
              </Button>
            </div>
            <Button
              aria-label="Share"
              onClick={handleShare}
              size="icon"
              variant="ghost"
            >
              <Share2 className="size-5" />
            </Button>
            <a
              aria-label="Download image"
              download
              href={imageUrl}
              rel="noopener noreferrer"
              target="_blank"
            >
              <Button size="icon" variant="ghost">
                <Download className="size-5" />
              </Button>
            </a>
          </div>

          {/* Image metadata */}
          <div className="mt-2 flex flex-col items-start gap-1">
            <p className="font-medium text-lg">{title}</p>
            <p className="text-muted-foreground text-sm">{description}</p>
            <p className="text-muted-foreground text-sm">
              Uploaded:{' '}
              {formatDistanceToNow(new Date(date), { addSuffix: true })}
            </p>
          </div>
        </div>

        <DialogClose className="absolute top-2 right-2 z-20 rounded-full bg-background hover:ring focus:outline-none focus:ring">
          <X className="size-6" />
          <span className="sr-only">Close</span>
        </DialogClose>
      </DialogContent>
    </Dialog>
  );
}

function ImageContainer({
  imageUrl,
  title,
}: {
  imageUrl: string;
  title: string;
}) {
  return (
    <div className="relative z-10 mt-10 overflow-hidden rounded-md ring ring-ring/25 md:mt-0">
      <AspectRatio ratio={16 / 9}>
        <Image
          alt={title}
          className="w-full rounded-sm object-cover transition-all duration-300 hover:scale-105"
          height={1000}
          src={imageUrl}
          width={1000}
        />
      </AspectRatio>
    </div>
  );
}
