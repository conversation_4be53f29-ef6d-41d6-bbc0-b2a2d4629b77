import { PageContainer } from '@/components/custom/page-container';
import { tvVideos } from '@/config/app';
import VideoCard from './video-card';

export function VideoList() {
  return (
    <PageContainer className="py-8 pb-20 md:pb-16">
      <div className="flex flex-col gap-6 md:gap-10">
        <div className="grid grid-cols-1 gap-x-3 gap-y-6 md:grid-cols-2 lg:grid-cols-3 lg:gap-y-9">
          {tvVideos.map((video, index) => (
            <VideoCard
              key={video.title}
              {...video}
              priority={index === 0 || index === 1 || index === 2}
            />
          ))}
        </div>
      </div>
    </PageContainer>
  );
}
