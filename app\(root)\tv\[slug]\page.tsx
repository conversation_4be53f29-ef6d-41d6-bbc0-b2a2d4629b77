import { formatDistanceToNow } from 'date-fns';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Player from 'next-video/player';
import { PageContainer } from '@/components/custom/page-container';
import {
  Disclosure,
  DisclosureContent,
  DisclosureTrigger,
} from '@/components/ui/disclosure';
import { ScrollArea } from '@/components/ui/scroll-area';
import { tvVideos } from '@/config/app';
import SVideoCard from '@/features/tv/mobile-video-cards';
import SvideoCategory from '@/features/tv/mobile-video-category';
import { getVideo } from '@/features/tv/server';
import type { TVideo } from '@/types';

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const video = getVideo(slug);

  if (!video) {
    return notFound();
  }

  return {
    title: video.title,
    description: video.description,
    openGraph: {
      title: video.title,
      description: video.description,
      type: 'video.other',
      images: [
        {
          url: video.thumbnail,
          width: 1200,
          height: 630,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: video.title,
      description: video.description,
      images: [video.thumbnail],
      creator: '@rathonagency',
    },
  };
}

// ✅ Add this or adjust it to match your actual type
type VideoItem = {
  title: string;
  description: string;
  thumbnail: string;
  videoUrl: string;
  date: string;
};

export function generateStaticParams() {
  return tvVideos.map((video) => ({
    slug: video.slug,
  }));
}

export default async function TVPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const currentVideo = getVideo(slug);

  if (!currentVideo) {
    return <div>Video not found</div>;
  }

  const otherVideos = tvVideos.filter((v: TVideo) => v.slug !== slug);

  return (
    <PageContainer className="py-8 md:py-16">
      <div className="grid grid-cols-12 gap-4">
        <div className="col-span-12 md:col-span-8">
          <VideoPlayer video={currentVideo} />
        </div>
        <div className="col-span-12 py-6 md:col-span-4 md:py-0">
          <div className="relative flex flex-col gap-4 md:gap-2">
            <SvideoCategory />
            <div className="sticky top-0">
              <ScrollArea className="relative h-screen w-full py-2 focus-visible:ring-[1px]">
                <div className="flex flex-col gap-3 ">
                  {otherVideos.map((vid) => (
                    <SVideoCard key={vid.title} {...vid} />
                  ))}
                  {otherVideos.map((vid) => (
                    <SVideoCard key={vid.title} {...vid} />
                  ))}
                  {otherVideos.map((vid) => (
                    <SVideoCard key={vid.title} {...vid} />
                  ))}
                  {otherVideos.map((vid) => (
                    <SVideoCard key={vid.title} {...vid} />
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        </div>
      </div>
    </PageContainer>
  );
}

function VideoPlayer({ video }: { video: VideoItem }) {
  return (
    <div className="flex flex-col gap-6 p-1">
      <div className="relative overflow-hidden rounded-lg">
        <Player
          autoPlay
          className="rounded-lg bg-background"
          controls
          src={video.videoUrl}
          style={{
            borderRadius: '10px',
          }}
        />
      </div>
      <VideoDescription video={video} />
    </div>
  );
}

function VideoDescription({ video }: { video: VideoItem }) {
  return (
    <div className="flex flex-col gap-2">
      <h2 className="font-bold text-2xl">{video.title}</h2>
      <Disclosure className="w-full rounded-md bg-muted px-3">
        <DisclosureTrigger className="flex cursor-pointer">
          <button
            className="w-full cursor-pointer py-2 text-left text-sm"
            type="button"
          >
            Show more
          </button>
        </DisclosureTrigger>
        <DisclosureContent>
          <div className="overflow-hidden pb-3">
            <p className="text-muted-foreground tracking-tight">
              {video.description}
            </p>
            <p className="text-muted-foreground tracking-tight">
              In this urgent video, we break down the latest market crash and
              what it means for investors, businesses, and the global economy.
              Whether you're in stocks, crypto, real estate, or just want to
              protect your money, this is essential viewing.
            </p>
            <p className="text-wrap">
              {formatDistanceToNow(new Date(video.date), { addSuffix: true })}
            </p>
          </div>
        </DisclosureContent>
      </Disclosure>
    </div>
  );
}
